package components

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/ui/styles"
	"arien-ai/pkg/types"
)

// HeaderComponent represents the header UI component
type HeaderComponent struct {
	theme   *styles.Theme
	width   int
	session *types.Session
	config  *types.Config
}

// NewHeaderComponent creates a new header component
func NewHeaderComponent(theme *styles.Theme, session *types.Session, config *types.Config) *HeaderComponent {
	return &HeaderComponent{
		theme:   theme,
		session: session,
		config:  config,
	}
}

// SetWidth sets the width of the header component
func (h *HeaderComponent) SetWidth(width int) {
	h.width = width
}

// Update updates the header component with new session data
func (h *HeaderComponent) Update(session *types.Session) {
	h.session = session
}

// Render renders the header component
func (h *HeaderComponent) Render() string {
	if h.width <= 0 {
		return ""
	}

	// Create header sections
	leftSection := h.renderLeftSection()
	centerSection := h.renderCenterSection()
	rightSection := h.renderRightSection()

	// Calculate available space
	leftWidth := lipgloss.Width(leftSection)
	rightWidth := lipgloss.Width(rightSection)
	centerWidth := h.width - leftWidth - rightWidth - 4 // 4 for spacing

	if centerWidth < 0 {
		centerWidth = 0
	}

	// Truncate center section if needed
	if lipgloss.Width(centerSection) > centerWidth {
		centerSection = truncateString(centerSection, centerWidth)
	}

	// Combine sections
	header := leftSection + strings.Repeat(" ", 2) + 
		padStringCenter(centerSection, centerWidth) + 
		strings.Repeat(" ", 2) + rightSection

	// Apply header style
	return h.theme.Styles.HeaderContainer.Width(h.width).Render(header)
}

// renderLeftSection renders the left section of the header
func (h *HeaderComponent) renderLeftSection() string {
	appName := h.theme.Styles.HeaderText.Render("Arien AI")

	if h.session != nil && h.session.Name != "" && h.session.Name != "default" {
		sessionName := h.theme.Styles.TextSecondary.Render(fmt.Sprintf("· %s", h.session.Name))
		return appName + " " + sessionName
	}

	return appName
}

// renderCenterSection renders the center section of the header
func (h *HeaderComponent) renderCenterSection() string {
	if h.config == nil {
		return ""
	}

	// Show current working directory in a minimal way
	workingDir := getCurrentWorkingDir()
	if workingDir != "" {
		// Show only the last directory name
		parts := strings.Split(workingDir, string(os.PathSeparator))
		if len(parts) > 0 {
			lastDir := parts[len(parts)-1]
			if lastDir == "" && len(parts) > 1 {
				lastDir = parts[len(parts)-2]
			}
			if lastDir != "" {
				dirStyle := h.theme.Styles.TextMuted
				return dirStyle.Render(lastDir)
			}
		}
	}

	return ""
}

// renderRightSection renders the right section of the header
func (h *HeaderComponent) renderRightSection() string {
	if h.config == nil {
		return ""
	}

	var parts []string

	// Provider and model in a minimal format
	if h.config.LLM.Provider != "" {
		providerText := h.config.LLM.Provider
		if h.config.LLM.Model != "" {
			// Show only model name, not full provider/model
			modelParts := strings.Split(h.config.LLM.Model, "/")
			modelName := modelParts[len(modelParts)-1]
			providerText = modelName
		}
		parts = append(parts, h.theme.Styles.TextSecondary.Render(providerText))
	}

	// Current time
	timeText := time.Now().Format("15:04")
	parts = append(parts, h.theme.Styles.TextMuted.Render(timeText))

	return strings.Join(parts, " · ")
}

// GetHeight returns the height of the header component
func (h *HeaderComponent) GetHeight() int {
	return 2 // Minimal header height
}

// StatusIndicator represents different status types
type StatusIndicator int

const (
	StatusIdle StatusIndicator = iota
	StatusThinking
	StatusExecuting
	StatusError
	StatusSuccess
)

// RenderWithStatus renders the header with a status indicator
func (h *HeaderComponent) RenderWithStatus(status StatusIndicator, message string) string {
	baseHeader := h.Render()
	
	if message == "" {
		return baseHeader
	}

	// Create status line
	var statusStyle lipgloss.Style
	var icon string

	switch status {
	case StatusThinking:
		statusStyle = h.theme.Styles.TextPrimary
		icon = "🤔"
	case StatusExecuting:
		statusStyle = h.theme.Styles.TextWarning
		icon = "⚡"
	case StatusError:
		statusStyle = h.theme.Styles.ErrorMessage
		icon = "❌"
	case StatusSuccess:
		statusStyle = h.theme.Styles.SuccessMessage
		icon = "✅"
	default:
		statusStyle = h.theme.Styles.TextMuted
		icon = "ℹ️"
	}

	statusText := statusStyle.Render(fmt.Sprintf("%s %s", icon, message))
	statusLine := h.theme.Styles.StatusContainer.Width(h.width).Render(statusText)

	return baseHeader + "\n" + statusLine
}

// Helper functions

func getCurrentWorkingDir() string {
	dir, err := os.Getwd()
	if err != nil {
		return ""
	}
	return dir
}

func truncateString(s string, maxLength int) string {
	if len(s) <= maxLength {
		return s
	}
	if maxLength <= 3 {
		return "..."
	}
	return s[:maxLength-3] + "..."
}

func padStringCenter(s string, width int) string {
	if len(s) >= width {
		return s
	}
	
	totalPadding := width - len(s)
	leftPadding := totalPadding / 2
	rightPadding := totalPadding - leftPadding
	
	return strings.Repeat(" ", leftPadding) + s + strings.Repeat(" ", rightPadding)
}

func formatTokenCount(tokens int) string {
	if tokens < 1000 {
		return fmt.Sprintf("%dt", tokens)
	}
	if tokens < 1000000 {
		return fmt.Sprintf("%.1fkt", float64(tokens)/1000)
	}
	return fmt.Sprintf("%.1fmt", float64(tokens)/1000000)
}

// HeaderInfo contains information to display in the header
type HeaderInfo struct {
	SessionName   string
	WorkingDir    string
	Provider      string
	Model         string
	MessageCount  int
	TokenCount    int
	Status        string
	StatusMessage string
}

// RenderWithInfo renders the header with custom information
func (h *HeaderComponent) RenderWithInfo(info HeaderInfo) string {
	if h.width <= 0 {
		return ""
	}

	// Left section
	appName := h.theme.Styles.HeaderText.Render("🤖 Arien-AI")
	leftSection := appName
	if info.SessionName != "" {
		sessionName := h.theme.Styles.TextSecondary.Render(fmt.Sprintf("| %s", info.SessionName))
		leftSection += " " + sessionName
	}

	// Center section
	centerSection := ""
	if info.WorkingDir != "" {
		// Show only the last two parts of the path
		parts := strings.Split(info.WorkingDir, string(filepath.Separator))
		displayDir := info.WorkingDir
		if len(parts) > 2 {
			displayDir = ".../" + strings.Join(parts[len(parts)-2:], "/")
		}
		centerSection = h.theme.Styles.TextMuted.Render(fmt.Sprintf("📁 %s", displayDir))
	}

	// Right section
	var rightParts []string
	
	if info.Provider != "" && info.Model != "" {
		providerText := fmt.Sprintf("%s/%s", info.Provider, info.Model)
		rightParts = append(rightParts, h.theme.Styles.TextPrimary.Render(providerText))
	}
	
	if info.MessageCount > 0 {
		msgText := fmt.Sprintf("%d msgs", info.MessageCount)
		rightParts = append(rightParts, h.theme.Styles.TextSecondary.Render(msgText))
	}
	
	if info.TokenCount > 0 {
		tokenText := formatTokenCount(info.TokenCount)
		rightParts = append(rightParts, h.theme.Styles.TextMuted.Render(tokenText))
	}
	
	timeText := time.Now().Format("15:04")
	rightParts = append(rightParts, h.theme.Styles.TextMuted.Render(timeText))
	
	rightSection := strings.Join(rightParts, " | ")

	// Calculate spacing
	leftWidth := lipgloss.Width(leftSection)
	rightWidth := lipgloss.Width(rightSection)
	centerWidth := h.width - leftWidth - rightWidth - 4

	if centerWidth < 0 {
		centerWidth = 0
	}

	if lipgloss.Width(centerSection) > centerWidth {
		centerSection = truncateString(centerSection, centerWidth)
	}

	// Combine sections
	header := leftSection + strings.Repeat(" ", 2) + 
		padStringCenter(centerSection, centerWidth) + 
		strings.Repeat(" ", 2) + rightSection

	result := h.theme.Styles.HeaderContainer.Width(h.width).Render(header)

	// Add status line if present
	if info.Status != "" && info.StatusMessage != "" {
		var statusStyle lipgloss.Style
		var icon string

		switch info.Status {
		case "thinking":
			statusStyle = h.theme.Styles.TextPrimary
			icon = "🤔"
		case "executing":
			statusStyle = h.theme.Styles.TextWarning
			icon = "⚡"
		case "error":
			statusStyle = h.theme.Styles.ErrorMessage
			icon = "❌"
		case "success":
			statusStyle = h.theme.Styles.SuccessMessage
			icon = "✅"
		default:
			statusStyle = h.theme.Styles.TextMuted
			icon = "ℹ️"
		}

		statusText := statusStyle.Render(fmt.Sprintf("%s %s", icon, info.StatusMessage))
		statusLine := h.theme.Styles.StatusContainer.Width(h.width).Render(statusText)
		result += "\n" + statusLine
	}

	return result
}
