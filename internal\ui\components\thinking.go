package components

import (
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/ui/styles"
)

// ThinkingComponent represents the thinking animation component
type ThinkingComponent struct {
	theme     *styles.Theme
	active    bool
	frame     int
	message   string
	startTime time.Time
	width     int
}

// ThinkingTickMsg represents a tick message for animation
type ThinkingTickMsg struct{}

// NewThinkingComponent creates a new thinking component
func NewThinkingComponent(theme *styles.Theme) *ThinkingComponent {
	return &ThinkingComponent{
		theme: theme,
	}
}

// Start starts the thinking animation
func (t *ThinkingComponent) Start(message string) tea.Cmd {
	t.active = true
	t.frame = 0
	t.message = message
	t.startTime = time.Now()
	return t.tick()
}

// Stop stops the thinking animation
func (t *ThinkingComponent) Stop() {
	t.active = false
}

// IsActive returns whether the animation is active
func (t *ThinkingComponent) IsActive() bool {
	return t.active
}

// SetWidth sets the width of the component
func (t *ThinkingComponent) SetWidth(width int) {
	t.width = width
}

// Update handles messages for the thinking component
func (t *ThinkingComponent) Update(msg tea.Msg) tea.Cmd {
	switch msg.(type) {
	case ThinkingTickMsg:
		if t.active {
			t.frame++
			return t.tick()
		}
	}
	return nil
}

// tick returns a command to send a tick message after a delay
func (t *ThinkingComponent) tick() tea.Cmd {
	return tea.Tick(100*time.Millisecond, func(time.Time) tea.Msg {
		return ThinkingTickMsg{}
	})
}

// Render renders the thinking animation
func (t *ThinkingComponent) Render() string {
	if !t.active {
		return ""
	}

	// Minimal dot animation frames
	ballFrames := []string{
		"   ●   ",
		"  ●●   ",
		" ●●●   ",
		"●●●●   ",
		"●●●●●  ",
		" ●●●●● ",
		"  ●●●●●",
		"   ●●●●",
		"    ●●●",
		"     ●●",
		"      ●",
		"       ",
	}

	// Get current frame
	currentFrame := ballFrames[t.frame%len(ballFrames)]
	
	// Calculate elapsed time
	elapsed := time.Since(t.startTime)
	elapsedText := fmt.Sprintf("%.1fs", elapsed.Seconds())

	// Create the thinking text
	thinkingText := t.message
	if thinkingText == "" {
		thinkingText = "thinking"
	}

	// Combine animation with text - minimal display
	animationStyle := t.theme.Styles.ThinkingDots
	textStyle := t.theme.Styles.TextSecondary

	content := fmt.Sprintf("%s %s",
		animationStyle.Render(currentFrame),
		textStyle.Render(thinkingText),
	)

	// Apply container styling
	container := lipgloss.NewStyle().
		Padding(0, 1).
		MarginLeft(2)

	if t.width > 0 {
		container = container.Width(t.width - 4)
	}

	return container.Render(content)
}

// RenderWithDots renders thinking animation with dots
func (t *ThinkingComponent) RenderWithDots() string {
	if !t.active {
		return ""
	}

	// Dots animation
	dotsCount := (t.frame % 4) + 1
	dots := strings.Repeat(".", dotsCount) + strings.Repeat(" ", 3-dotsCount)

	// Calculate elapsed time
	elapsed := time.Since(t.startTime)
	elapsedText := fmt.Sprintf("%.1fs", elapsed.Seconds())

	// Create the thinking text
	thinkingText := t.message
	if thinkingText == "" {
		thinkingText = "Thinking"
	}

	// Combine animation with text
	textStyle := t.theme.Styles.TextSecondary
	dotsStyle := t.theme.Styles.ThinkingDots
	timeStyle := t.theme.Styles.TextMuted

	content := fmt.Sprintf("%s%s %s",
		textStyle.Render(thinkingText),
		dotsStyle.Render(dots),
		timeStyle.Render(elapsedText),
	)

	// Apply container styling
	container := lipgloss.NewStyle().
		Padding(0, 1).
		MarginLeft(2)

	if t.width > 0 {
		container = container.Width(t.width - 4)
	}

	return container.Render(content)
}

// RenderWithSpinner renders thinking animation with spinner
func (t *ThinkingComponent) RenderWithSpinner() string {
	if !t.active {
		return ""
	}

	// Spinner animation frames
	spinnerFrames := []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"}
	currentSpinner := spinnerFrames[t.frame%len(spinnerFrames)]

	// Calculate elapsed time
	elapsed := time.Since(t.startTime)
	elapsedText := fmt.Sprintf("%.1fs", elapsed.Seconds())

	// Create the thinking text
	thinkingText := t.message
	if thinkingText == "" {
		thinkingText = "Processing"
	}

	// Combine animation with text
	spinnerStyle := t.theme.Styles.LoadingSpinner
	textStyle := t.theme.Styles.TextSecondary
	timeStyle := t.theme.Styles.TextMuted

	content := fmt.Sprintf("%s %s %s",
		spinnerStyle.Render(currentSpinner),
		textStyle.Render(thinkingText),
		timeStyle.Render(elapsedText),
	)

	// Apply container styling
	container := lipgloss.NewStyle().
		Padding(0, 1).
		MarginLeft(2)

	if t.width > 0 {
		container = container.Width(t.width - 4)
	}

	return container.Render(content)
}

// RenderWithProgress renders thinking animation with progress bar
func (t *ThinkingComponent) RenderWithProgress(current, total int) string {
	if !t.active {
		return ""
	}

	// Calculate progress
	progress := float64(current) / float64(total)
	if progress > 1.0 {
		progress = 1.0
	}

	// Create progress bar
	barWidth := 20
	filled := int(progress * float64(barWidth))
	empty := barWidth - filled

	progressBar := strings.Repeat("█", filled) + strings.Repeat("░", empty)
	progressText := fmt.Sprintf("[%s] %d/%d", progressBar, current, total)

	// Calculate elapsed time
	elapsed := time.Since(t.startTime)
	elapsedText := fmt.Sprintf("%.1fs", elapsed.Seconds())

	// Create the thinking text
	thinkingText := t.message
	if thinkingText == "" {
		thinkingText = "Processing"
	}

	// Combine progress with text
	textStyle := t.theme.Styles.TextSecondary
	progressStyle := t.theme.Styles.ThinkingDots
	timeStyle := t.theme.Styles.TextMuted

	content := fmt.Sprintf("%s\n%s %s",
		textStyle.Render(thinkingText),
		progressStyle.Render(progressText),
		timeStyle.Render(elapsedText),
	)

	// Apply container styling
	container := lipgloss.NewStyle().
		Padding(0, 1).
		MarginLeft(2)

	if t.width > 0 {
		container = container.Width(t.width - 4)
	}

	return container.Render(content)
}

// RenderCustom renders thinking animation with custom frames
func (t *ThinkingComponent) RenderCustom(frames []string, message string) string {
	if !t.active || len(frames) == 0 {
		return ""
	}

	// Get current frame
	currentFrame := frames[t.frame%len(frames)]

	// Calculate elapsed time
	elapsed := time.Since(t.startTime)
	elapsedText := fmt.Sprintf("%.1fs", elapsed.Seconds())

	// Use provided message or default
	thinkingText := message
	if thinkingText == "" {
		thinkingText = t.message
	}
	if thinkingText == "" {
		thinkingText = "Working"
	}

	// Combine animation with text
	animationStyle := t.theme.Styles.ThinkingDots
	textStyle := t.theme.Styles.TextSecondary
	timeStyle := t.theme.Styles.TextMuted

	content := fmt.Sprintf("%s %s %s",
		animationStyle.Render(currentFrame),
		textStyle.Render(thinkingText),
		timeStyle.Render(elapsedText),
	)

	// Apply container styling
	container := lipgloss.NewStyle().
		Padding(0, 1).
		MarginLeft(2)

	if t.width > 0 {
		container = container.Width(t.width - 4)
	}

	return container.Render(content)
}

// GetElapsedTime returns the elapsed time since animation started
func (t *ThinkingComponent) GetElapsedTime() time.Duration {
	if !t.active {
		return 0
	}
	return time.Since(t.startTime)
}

// SetMessage updates the thinking message
func (t *ThinkingComponent) SetMessage(message string) {
	t.message = message
}

// GetMessage returns the current thinking message
func (t *ThinkingComponent) GetMessage() string {
	return t.message
}

// AnimationType represents different animation types
type AnimationType int

const (
	AnimationBalls AnimationType = iota
	AnimationDots
	AnimationSpinner
	AnimationProgress
	AnimationCustom
)

// RenderWithType renders thinking animation with specified type
func (t *ThinkingComponent) RenderWithType(animType AnimationType, params ...interface{}) string {
	switch animType {
	case AnimationBalls:
		return t.Render()
	case AnimationDots:
		return t.RenderWithDots()
	case AnimationSpinner:
		return t.RenderWithSpinner()
	case AnimationProgress:
		if len(params) >= 2 {
			if current, ok := params[0].(int); ok {
				if total, ok := params[1].(int); ok {
					return t.RenderWithProgress(current, total)
				}
			}
		}
		return t.RenderWithSpinner()
	case AnimationCustom:
		if len(params) >= 1 {
			if frames, ok := params[0].([]string); ok {
				message := ""
				if len(params) >= 2 {
					if msg, ok := params[1].(string); ok {
						message = msg
					}
				}
				return t.RenderCustom(frames, message)
			}
		}
		return t.Render()
	default:
		return t.Render()
	}
}
