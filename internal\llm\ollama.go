package llm

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/sasha<PERSON>nov/go-openai"

	"arien-ai/pkg/types"
)

// OllamaClient implements the Client interface for Ollama API
type OllamaClient struct {
	client *openai.Client
	config types.LLMConfig
}

// NewOllamaClient creates a new Ollama client
func NewOllamaClient(config types.LLMConfig) (*OllamaClient, error) {
	// Set default base URL if not provided
	baseURL := config.BaseURL
	if baseURL == "" {
		baseURL = "http://localhost:11434/v1"
	}

	// Create OpenAI client with Ollama configuration and improved HTTP settings
	// Ollama uses OpenAI-compatible API, so we can use the same client
	clientConfig := openai.DefaultConfig("ollama") // API key not needed for local Ollama
	clientConfig.BaseURL = baseURL

	// Configure HTTP client with better settings for local connections
	clientConfig.HTTPClient = &http.Client{
		Timeout: 120 * time.Second, // Longer timeout for local models
		Transport: &http.Transport{
			MaxIdleConns:        50,
			MaxIdleConnsPerHost: 5,
			IdleConnTimeout:     60 * time.Second,
			TLSHandshakeTimeout: 5 * time.Second,
			DisableKeepAlives:  false,
		},
	}

	client := openai.NewClientWithConfig(clientConfig)

	return &OllamaClient{
		client: client,
		config: config,
	}, nil
}

// Chat sends a chat completion request to Ollama
func (c *OllamaClient) Chat(ctx context.Context, messages []*types.Message, tools []Tool) (*types.Message, error) {
	if err := ValidateMessages(messages); err != nil {
		return nil, fmt.Errorf("invalid messages: %w", err)
	}

	if err := ValidateTools(tools); err != nil {
		return nil, fmt.Errorf("invalid tools: %w", err)
	}

	// Convert messages to OpenAI format
	openAIMessages := ConvertMessagesToOpenAI(messages)

	// Create chat completion request
	req := openai.ChatCompletionRequest{
		Model:       c.config.Model,
		Messages:    convertToOpenAIMessages(openAIMessages),
		Temperature: c.config.Temperature,
		MaxTokens:   c.config.MaxTokens,
	}

	// Add tools if provided (note: tool support may vary by Ollama model)
	if len(tools) > 0 {
		req.Tools = convertToOpenAITools(tools)
		req.ToolChoice = "auto"
	}

	// Send request
	resp, err := c.client.CreateChatCompletion(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("no choices returned from API")
	}

	choice := resp.Choices[0]
	
	// Create response message
	message := types.NewAssistantMessage(choice.Message.Content)
	
	// Set token usage (if available)
	if resp.Usage.TotalTokens > 0 {
		message.SetTokenUsage(&types.TokenUsage{
			PromptTokens:     resp.Usage.PromptTokens,
			CompletionTokens: resp.Usage.CompletionTokens,
			TotalTokens:      resp.Usage.TotalTokens,
		})
	}

	// Handle tool calls (if supported by the model)
	if len(choice.Message.ToolCalls) > 0 {
		for _, tc := range choice.Message.ToolCalls {
			toolCall := types.ToolCall{
				ID:   tc.ID,
				Type: string(tc.Type),
				Function: types.FunctionCall{
					Name:      tc.Function.Name,
					Arguments: []byte(tc.Function.Arguments),
				},
			}
			message.AddToolCall(toolCall)
		}
	}

	return message, nil
}

// Stream sends a streaming chat completion request to Ollama
func (c *OllamaClient) Stream(ctx context.Context, messages []*types.Message, tools []Tool, callback func(*types.Message)) error {
	if err := ValidateMessages(messages); err != nil {
		return fmt.Errorf("invalid messages: %w", err)
	}

	if err := ValidateTools(tools); err != nil {
		return fmt.Errorf("invalid tools: %w", err)
	}

	// Convert messages to OpenAI format
	openAIMessages := ConvertMessagesToOpenAI(messages)

	// Create streaming chat completion request
	req := openai.ChatCompletionRequest{
		Model:       c.config.Model,
		Messages:    convertToOpenAIMessages(openAIMessages),
		Temperature: c.config.Temperature,
		MaxTokens:   c.config.MaxTokens,
		Stream:      true,
	}

	// Add tools if provided
	if len(tools) > 0 {
		req.Tools = convertToOpenAITools(tools)
		req.ToolChoice = "auto"
	}

	// Create stream
	stream, err := c.client.CreateChatCompletionStream(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to create chat completion stream: %w", err)
	}
	defer stream.Close()

	// Process stream
	var contentBuilder strings.Builder
	var currentMessage *types.Message

	for {
		response, err := stream.Recv()
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			return fmt.Errorf("error receiving stream: %w", err)
		}

		if len(response.Choices) == 0 {
			continue
		}

		choice := response.Choices[0]
		delta := choice.Delta

		// Initialize message if needed
		if currentMessage == nil {
			currentMessage = types.NewAssistantMessage("")
		}

		// Handle content delta
		if delta.Content != "" {
			contentBuilder.WriteString(delta.Content)
			currentMessage.Content = contentBuilder.String()
			
			// Call callback with updated message
			if callback != nil {
				callback(currentMessage)
			}
		}

		// Handle tool calls
		if len(delta.ToolCalls) > 0 {
			for _, tc := range delta.ToolCalls {
				toolCall := types.ToolCall{
					ID:   tc.ID,
					Type: string(tc.Type),
					Function: types.FunctionCall{
						Name:      tc.Function.Name,
						Arguments: []byte(tc.Function.Arguments),
					},
				}
				currentMessage.AddToolCall(toolCall)
			}
		}

		// Handle finish reason
		if choice.FinishReason != "" {
			break
		}
	}

	// Final callback with complete message
	if callback != nil && currentMessage != nil {
		callback(currentMessage)
	}

	return nil
}

// GetModels returns available models from Ollama
func (c *OllamaClient) GetModels(ctx context.Context) ([]Model, error) {
	// Try to get models from Ollama API
	models, err := c.client.ListModels(ctx)
	if err != nil {
		// If API call fails, return common Ollama models
		return c.getDefaultModels(), nil
	}

	var result []Model
	for _, model := range models.Models {
		result = append(result, Model{
			ID:          model.ID,
			Name:        model.ID,
			Description: fmt.Sprintf("Ollama model: %s", model.ID),
			Provider:    "ollama",
			Capabilities: []string{"chat"},
		})
	}

	// If no models returned, use defaults
	if len(result) == 0 {
		return c.getDefaultModels(), nil
	}

	return result, nil
}

// getDefaultModels returns a list of common Ollama models
func (c *OllamaClient) getDefaultModels() []Model {
	return []Model{
		{
			ID:          "llama3.3",
			Name:        "Llama 3.3",
			Description: "Meta's Llama 3.3 model",
			Provider:    "ollama",
			Capabilities: []string{"chat"},
		},
		{
			ID:          "deepseek-r1",
			Name:        "DeepSeek R1",
			Description: "DeepSeek R1 reasoning model",
			Provider:    "ollama",
			Capabilities: []string{"chat", "reasoning"},
		},
		{
			ID:          "qwen2.5",
			Name:        "Qwen 2.5",
			Description: "Alibaba's Qwen 2.5 model",
			Provider:    "ollama",
			Capabilities: []string{"chat"},
		},
		{
			ID:          "mistral",
			Name:        "Mistral",
			Description: "Mistral AI model",
			Provider:    "ollama",
			Capabilities: []string{"chat"},
		},
		{
			ID:          "codellama",
			Name:        "Code Llama",
			Description: "Meta's Code Llama for code generation",
			Provider:    "ollama",
			Capabilities: []string{"chat", "code"},
		},
	}
}

// ValidateConfig validates the Ollama client configuration
func (c *OllamaClient) ValidateConfig() error {
	if c.config.Model == "" {
		return fmt.Errorf("model is required")
	}

	// Check if Ollama is accessible (optional validation)
	// This could be implemented to ping the Ollama API

	return nil
}

// GetProvider returns the provider name
func (c *OllamaClient) GetProvider() string {
	return "ollama"
}

// Close closes the client and cleans up resources
func (c *OllamaClient) Close() error {
	// No cleanup needed for HTTP client
	return nil
}

// IsOllamaRunning checks if Ollama is running and accessible
func (c *OllamaClient) IsOllamaRunning(ctx context.Context) bool {
	_, err := c.client.ListModels(ctx)
	return err == nil
}

// PullModel pulls a model in Ollama (if supported by the API)
func (c *OllamaClient) PullModel(ctx context.Context, modelName string) error {
	// This would require a custom implementation as the go-openai client
	// doesn't support Ollama's pull endpoint
	// For now, return an error indicating manual pull is needed
	return fmt.Errorf("please pull the model manually using: ollama pull %s", modelName)
}
